import uvicorn
from nyx.main import create_app

if __name__ == "__main__":
    uvicorn.run(
        "nyx.main:create_app",  # Module and function to import and run
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False,  # Disable reload for better concurrency in production
        workers=1,  # Single worker to avoid shared state issues
        limit_concurrency=10,  # Limit concurrent requests
        limit_max_requests=1000,  # Restart worker after 1000 requests to prevent memory leaks
    )
