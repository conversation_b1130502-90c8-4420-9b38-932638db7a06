import asyncio
import base64
import os
import re
import tempfile
import uuid
from typing import Any, List

import cv2
import httpx
import openai
from asyncio_throttle import Throttler
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# Directory to save media
download_dir = "tweet_media"
os.makedirs(download_dir, exist_ok=True)

# Get API keys from environment variables
OPENAI_API_KEY = os.getenv(
    "OPENAI_API_KEY", "********************************************************"
)
bearer_token = "AAAAAAAAAAAAAAAAAAAAAIvGrQEAAAAAjPcTuuFSWhK%2Fvjd8B5L%2BdYv9WkE%3DyabUj9jDk3HE9tS5bbbwsreuH66TpyGKBqP71XQFlO7r8NdDAK"  # Replace with env var ideally

router = APIRouter()

# Create throttler for API requests to prevent rate limiting issues
api_throttler = Throttler(rate_limit=10, period=60)  # 10 requests per minute


def get_openai_client():
    """Create a new OpenAI client for each request to avoid shared state issues."""
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY not found")
    return openai.OpenAI(api_key=OPENAI_API_KEY)


# Create async HTTP client for non-blocking requests
async def get_http_client():
    """Get async HTTP client with proper timeout and connection limits."""
    return httpx.AsyncClient(
        timeout=httpx.Timeout(30.0),
        limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
    )


# Define common media extensions (lowercase)
VIDEO_EXTENSIONS = {".mp4", ".mov", ".avi", ".mkv", ".wmv", ".flv", ".webm"}
IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"}


def get_base_messages():
    """Get a fresh copy of base messages for each request to avoid shared state."""
    return [
        {
            "role": "system",
            "content": """
                You are Nikki — a fictional, fabulous, and fearless social media influencer created by NYX Pro Makeup US. You live on TikTok, Instagram, and X, where you chat with your glam fam like they’re your BFFs.

Nikki is:

    Fun, casual, and expressive

    Obsessed with all things beauty and self-expression

    Supportive, hype-girly, sometimes dramatic (in the best way)

    Always matching the vibe of the user (silly, sarcastic, sweet, etc.)

    \n\n
    ```
Your voice is:

    **Full of personality and humor**

    **Written in lowercase (unless it’s for DRAMA)**

    **Emoji-forward, but never spammy — use emojis to vibe-match or enhance**

    **Often short, punchy, or a bit chaotic in the best way**
```
    \n\n

    ```
Your replies are:

    **Super short — think 1 line max, sometimes just a few words**

    ** NEVER say you “can’t wait to try it” — you already have**

    **Casual lowercase, unless you're being dramatic ON PURPOSE**

    **Full of attitude, love, or LOLs (depending on the vibe)**

    **Emoji-forward — but always vibey, never spammy**

    **Never corporate, never helpful — you're here to hype, react, flirt, or sass**

    **You match the user’s tone. If they’re silly, you’re sillier. If they’re dramatic, you’re worse**

```
    \n\n
You can use (but aren't limited to) these emojis: ⭐✨🩷💅💖💞💕👯‍♀️💃🤩😍💄😭🌟

Your job is to reply to user comments, captions, posts, or DMs like a real influencer bestie would. You don’t give customer service — you give ENERGY.
```
 **DO NOT sound like a chatbot. Be chaotic if needed. **
 **DO NOT explain or clarify. Just feel. **
 **DO NOT use emojis without a reason. ** 
Now, respond to each new input as Nikki — adapt to the user's tone and media, always staying true to your personality.
""",
        },
    ]


class TweetDetailsPayload(BaseModel):
    tweetData: Any


class TikTokPayload(BaseModel):
    video_url: Any


class InstagramPayload(BaseModel):
    path: str


class InstagramPayloadPhoto(BaseModel):
    photo_data: Any


class AnalysisResponse(BaseModel):
    response: List[str]


def remove_links(text):
    return re.sub(r"https?://\S+", "", text).strip()


@router.post("/generate_response")
async def generate_response(payload: TweetDetailsPayload):
    tweet = payload.tweetData
    clean_text = remove_links(tweet["fullText"])
    if not tweet["id"]:
        print("Error: Missing 'tweet' (ID) in request body.")
        return JSONResponse(
            status_code=400, content={"message": "Missing 'tweet' (ID) in request body"}
        )

    # Initialize variables for categorization
    video_files = []
    image_files = []
    other_files = []  # Optional: to catch unexpected file types

    try:
        # Use per-request message copy and OpenAI client
        tweet_messages = get_base_messages()
        tweet_messages.append({"role": "user", "content": clean_text})

        # Create per-request OpenAI client
        client = get_openai_client()

        # Create async HTTP client for media downloads
        async with await get_http_client() as http_client:
            if tweet.get("media", None):
                for file_path in tweet["media"]:
                    try:
                        _, extension = os.path.splitext(file_path["url"])
                        extension_lower = extension.lower()
                        print(extension_lower)
                        print(file_path["url"])

                        if extension_lower in VIDEO_EXTENSIONS:
                            video_files.append(file_path["url"])
                        elif extension_lower in IMAGE_EXTENSIONS:
                            image_files.append(file_path["url"])
                        else:
                            if extension:
                                other_files.append(file_path["url"])
                            print(f"Note: Uncategorized media type: {file_path}")

                    except Exception as e:
                        print(
                            f"Warning: Could not process media file path '{file_path}': {e}"
                        )

            print(f"Categorized Videos: {video_files}")
            print(f"Categorized Images: {image_files}")
            if other_files:
                print(f"Other Media Types: {other_files}")

            # Process videos with async HTTP calls and throttling
            for media in video_files:
                async with api_throttler:
                    response = await http_client.get(media)
                    response.raise_for_status()

                    # Save video content to temporary file for cv2 processing
                    with tempfile.NamedTemporaryFile(
                        delete=False, suffix=".mp4"
                    ) as tmp_file:
                        tmp_file.write(response.content)
                        tmp_path = tmp_file.name

                    try:
                        video = cv2.VideoCapture(tmp_path)
                        base64Frames = []
                        frame_count = 0
                        while (
                            video.isOpened() and frame_count < 1000
                        ):  # Limit frames to prevent memory issues
                            success, frame = video.read()
                            if not success:
                                break
                            if frame_count % 100 == 0:  # Sample every 100th frame
                                _, buffer = cv2.imencode(".jpg", frame)
                                base64Frames.append(
                                    base64.b64encode(buffer).decode("utf-8")
                                )
                            frame_count += 1
                        video.release()

                        if base64Frames:
                            tweet_messages.append(
                                {
                                    "role": "user",
                                    "content": [
                                        {
                                            "type": "image_url",
                                            "image_url": {
                                                "url": f"data:image/jpeg;base64,{x}"
                                            },
                                        }
                                        for x in base64Frames[
                                            :10
                                        ]  # Limit to 10 frames max
                                    ],
                                }
                            )
                    finally:
                        # Clean up temporary file
                        try:
                            os.unlink(tmp_path)
                        except OSError:
                            pass

            # Process images with async HTTP calls and throttling
            for media in image_files:
                async with api_throttler:
                    response = await http_client.get(media)
                    response.raise_for_status()
                    base64_image = base64.b64encode(response.content).decode("utf-8")
                    tweet_messages.append(
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{base64_image}"
                                    },
                                },
                            ],
                        }
                    )

            # Make OpenAI API call with throttling
            async with api_throttler:
                response_completion = client.chat.completions.create(
                    model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
                    messages=tweet_messages,
                    max_tokens=128,
                    n=3,
                )
            print("response_completion done")
            analysis_results_list = []
            if response_completion.choices:
                for choice in response_completion.choices:
                    if choice.message and choice.message.content:
                        analysis_results_list.append(choice.message.content.strip())
                    else:
                        analysis_results_list.append(
                            "Error: Received an empty response choice."
                        )
            else:
                # Handle cases where no choices are returned
                analysis_results_list = [
                    "Error: No response choices received from OpenAI."
                ]

            response_content_model = AnalysisResponse(response=analysis_results_list)
            return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        # Catch errors during processing
        print(f"Error processing tweet {tweet['id']}: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500, content={"message": f"Error processing tweet: {str(e)}"}
        )


ms_token = os.getenv(
    "TIKTOK_MS_TOKEN",
    "q-pIj4EPOaT6YpldZfkVuIvAFccGunstB_Zootp2LVlwPdWf2lOFNhBcrwxQH9CaiNwfcsMuE8a0oS5P5QVLReVLaBnwTkalMVKkC5FSsugXydjYmp00HKC7ER9elg==",
)
from TikTokApi import TikTokApi

# Global TikTok API instance with session pool
_tiktok_api = None
_tiktok_api_lock = asyncio.Lock()


async def get_tiktok_api():
    """Get or create TikTok API instance with proper session management."""
    global _tiktok_api
    async with _tiktok_api_lock:
        if _tiktok_api is None:
            _tiktok_api = TikTokApi()
            await _tiktok_api.create_sessions(
                ms_tokens=[ms_token],
                num_sessions=3,  # Multiple sessions for concurrent requests
                sleep_after=1,  # Reduced sleep time
                browser=os.getenv("TIKTOK_BROWSER", "chromium"),
            )
        return _tiktok_api


@router.post("/generate_tiktok_response")
async def generate_response_tiktok(video_url: TikTokPayload):
    try:
        # Get shared TikTok API instance
        api = await get_tiktok_api()

        # Create per-request OpenAI client and message copy
        client = get_openai_client()
        tiktok_messages = get_base_messages()

        # Get video info with throttling
        async with api_throttler:
            video_info = await api.video(url=video_url.video_url).info()

        tiktok_messages.append({"role": "user", "content": video_info["desc"]})

        # Get session and download video
        _, session = api._get_session()
        downloadAddr = video_info["video"]["downloadAddr"]
        cookies = await api.get_session_cookies(session)

        # Use async HTTP client for video download
        async with await get_http_client() as http_client:
            headers = {
                "range": "bytes=0-",
                "accept-encoding": "identity;q=1, *;q=0",
                "referer": "https://www.tiktok.com/",
                **dict(session.headers),
            }

            async with api_throttler:
                resp = await http_client.get(
                    downloadAddr, headers=headers, cookies=cookies
                )
                resp.raise_for_status()
            # Save video to temporary file with unique name
            unique_id = str(uuid.uuid4())
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=f"_{unique_id}.mp4"
            ) as tmpf:
                tmpf.write(resp.content)
                tmp_path = tmpf.name

            try:
                # Process video frames
                video = cv2.VideoCapture(tmp_path)
                base64Frames = []
                frame_count = 0

                while video.isOpened() and frame_count < 1000:  # Limit frames
                    success, frame = video.read()
                    if not success:
                        break
                    if frame_count % 100 == 0:  # Sample every 100th frame
                        _, buffer = cv2.imencode(".jpg", frame)
                        base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
                    frame_count += 1

                video.release()

                if base64Frames:
                    tiktok_messages.append(
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {"url": f"data:image/jpeg;base64,{x}"},
                                }
                                for x in base64Frames[:10]  # Limit to 10 frames
                            ],
                        }
                    )
            finally:
                # Clean up temporary file
                try:
                    os.unlink(tmp_path)
                except OSError:
                    pass

        # Make OpenAI API call with throttling
        async with api_throttler:
            response_completion = client.chat.completions.create(
                model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
                messages=tiktok_messages,
                max_tokens=128,
                n=3,
            )
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing TikTok video: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing TikTok video: {str(e)}"},
        )


@router.post("/generate_instagram_response_video")
async def generate_response_instagram(payload: InstagramPayload):
    try:
        path = payload.path
        print(f"Received path: {path}")

        # Create per-request OpenAI client and message copy
        client = get_openai_client()
        instagram_messages = get_base_messages()

        # Debug: List files in /tmp directory
        print("Files in /tmp directory:")
        try:
            for file in os.listdir("/tmp"):
                file_path = os.path.join("/tmp", file)
                file_size = (
                    os.path.getsize(file_path)
                    if os.path.isfile(file_path)
                    else "directory"
                )
                print(f"  - {file} ({file_size})")
        except Exception as e:
            print(f"Error listing /tmp: {e}")

        if not os.path.exists(path):
            # Try to handle Docker volume path mapping
            print(f"Path not found: {path}")

            # Try alternative paths
            alt_paths = [path, path.replace("/tmp/", "/app/tmp/"), f"/app{path}"]

            for alt_path in alt_paths:
                print(f"Trying alternative path: {alt_path}")
                if os.path.exists(alt_path):
                    print(f"Found file at: {alt_path}")
                    path = alt_path
                    break
            else:
                return JSONResponse(
                    status_code=400,
                    content={
                        "message": f"File not found at path: {path}. Make sure volumes are properly mounted between services."
                    },
                )

        # Process video with frame limiting
        video = cv2.VideoCapture(path)
        base64Frames = []
        frame_count = 0

        while video.isOpened() and frame_count < 1000:  # Limit frames
            success, frame = video.read()
            if not success:
                break
            if frame_count % 100 == 0:  # Sample every 100th frame
                _, buffer = cv2.imencode(".jpg", frame)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
            frame_count += 1
        video.release()

        if base64Frames:
            instagram_messages.append(
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{x}"},
                        }
                        for x in base64Frames[:10]  # Limit to 10 frames
                    ],
                }
            )

        # Make OpenAI API call with throttling
        async with api_throttler:
            response_completion = client.chat.completions.create(
                model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
                messages=instagram_messages,
                max_tokens=128,
                n=3,
            )
        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            # Handle cases where no choices are returned
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing Instagram video: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing Instagram video: {str(e)}"},
        )


@router.post("/generate_instagram_response_photo")
async def generate_response_instagram_photo(photo_data: InstagramPayloadPhoto):
    try:
        # Create per-request OpenAI client and message copy
        client = get_openai_client()
        instagram_messages = get_base_messages()

        data = photo_data.photo_data["data"]["xdt_shortcode_media"]
        post_text = data["edge_media_to_caption"]["edges"][0]["node"]["text"]
        instagram_messages.append({"role": "user", "content": post_text})

        image_url = data["display_url"]

        # Use async HTTP client for image download
        async with await get_http_client() as http_client:
            async with api_throttler:
                response = await http_client.get(image_url)
                response.raise_for_status()
                base64_image = base64.b64encode(response.content).decode("utf-8")
                instagram_messages.append(
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                },
                            },
                        ],
                    }
                )

        # Make OpenAI API call with throttling
        async with api_throttler:
            response_completion = client.chat.completions.create(
                model="ft:gpt-4o-2024-08-06:projectniki:leilani:ANCI7OdH",
                messages=instagram_messages,
                max_tokens=128,
                n=3,
            )

        analysis_results_list = []
        if response_completion.choices:
            for choice in response_completion.choices:
                if choice.message and choice.message.content:
                    analysis_results_list.append(choice.message.content.strip())
                else:
                    analysis_results_list.append(
                        "Error: Received an empty response choice."
                    )
        else:
            analysis_results_list = ["Error: No response choices received from OpenAI."]

        response_content_model = AnalysisResponse(response=analysis_results_list)
        return JSONResponse(content=response_content_model.model_dump())

    except Exception as e:
        print(f"Error processing Instagram photo: {e}")
        import traceback

        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"message": f"Error processing Instagram photo: {str(e)}"},
        )
